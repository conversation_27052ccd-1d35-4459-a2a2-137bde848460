<template>
  <bfDialog
    v-model="dlgVisible"
    :title="$t('nav.version')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    append-to-body
    class="versionInfo"
  >
    <div v-for="(item, i) in versions" :key="i" :class="['version-box', locale]">
      <div class="version-box-item">
        <span class="version-label" v-text="$t(`nav.${i}Version`) + ':'" />
        <span class="version-info" v-text="item.version" />
      </div>
      <div class="version-box-item">
        <span class="version-label" v-text="$t('nav.versionBuildTime') + ':'" />
        <span class="version-info" v-text="item.buildTime" />
      </div>
      <div class="version-box-item">
        <span class="version-label" v-text="$t('nav.versionGitTag') + ':'" />
        <span class="version-info" v-text="item.gitTag.slice(0, 8)" />
      </div>
      <div v-show="item.startRuningTime" class="version-box-item">
        <span class="version-label" v-text="$t('nav.startRuningTime') + ':'" />
        <span class="version-info" v-text="item.startRuningTime" />
      </div>
    </div>
  </bfDialog>
</template>

<script>
  import bfTime from '@/utils/time'
  import pkgJson from '~/package.json'
  import vueMixin from '@/utils/vueMixin'
  import bfDialog from '@/components/bfDialog/main'

  const version = pkgJson.version

  export default {
    name: 'BfVersion',
    components: {
      bfDialog,
    },
    props: {
      dialogVisible: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['update:dialogVisible'],
    mixins: [vueMixin],
    data() {
      return {
        visible: false,
        // CLIENT_BUILD_TIME,CLIENT_GIT_TAG, webpack.DefinePulgin define
        versions: {
          client: {
            version: version || '',
            buildTime: CLIENT_BUILD_TIME || '',
            gitTag: CLIENT_GIT_TAG || '',
            startRuningTime: '',
          },
          server: {
            version: '',
            buildTime: '',
            gitTag: '',
            startRuningTime: '',
          },
        },
      }
    },
    computed: {
      dlgVisible: {
        get() {
          // 如果有 dialogVisible props，使用它；否则使用 vueMixin 中的 visible
          return this.dialogVisible !== undefined ? this.dialogVisible : this.visible
        },
        set(val) {
          // 如果有 dialogVisible props，发出对应事件；否则设置 vueMixin 中的 visible
          if (this.dialogVisible !== undefined) {
            this.$emit('update:dialogVisible', val)
          } else {
            this.visible = val
          }
        },
      },
    },
    methods: {
      reloadServerVersion() {
        fetch('/server_version')
          .then(res => res.text())
          .then(data => {
            const res = data.match(
              /ver:(?<version>[0-9.].*?)\s?,?\s?build:(?<buildTime>\w*\S.*?),?\s?Git:(?<gitTag>\w*),?\s?Start:(?<startRuningTime>\w*\S.*?),?\s?UTC/
            )
            // IE，EDGE等浏览器不支持正则分组命名，生成对象
            if (!res.groups) {
              res.groups = {
                version: res[1],
                buildTime: res[2],
                gitTag: (res[3] || '').slice(0, 8),
                startRuningTime: res[4],
              }
            }
            this.versions.server = { ...res.groups }
            this.versions.server.buildTime = bfTime.localToUtcTime(this.versions.server.buildTime)
            this.versions.server.buildTime += ' UTC'
            this.versions.server.startRuningTime += ' UTC'
          })
          .catch(err => {
            console.error(err)
          })
      },
      serverReConnect() {
        this.reloadServerVersion()
      },
    },
    beforeMount() {
      // 在窗口打开时请求系统服务器的版本信息
      this.reloadServerVersion()
      bfglob.on('server.reconnect', this.serverReConnect)
    },
    beforeUnmount() {
      bfglob.off('server.reconnect', this.serverReConnect)
    },
  }
</script>

<style lang="scss">
  .versionInfo.el-dialog {
    width: 592px;
    height: 544px;

    .el-dialog__header span {
      text-align: center;
    }

    .el-dialog__body {
      width: 572px;
      height: 462px;
      padding: 56px 90px;
      margin: 10px;
      background: rgba(6, 121, 204, 0.46);
      box-shadow: inset 0 0 1.125rem rgba(14, 190, 255, 0.74);
      clip-path: polygon(0 0, 100% 0, 100% calc(100% - 30px), calc(100% - 30px) 100%, 0 100%);
    }

    .version-box {
      line-height: 30px;
      font-weight: 500;
      font-size: 20px;
      letter-spacing: 1px;
      .version-box-item {
        height: 50px;
        line-height: 50px;
        text-align: left;
        .version-label {
          display: inline-block;
          text-align: left;
          padding-right: 10px;
          color: white;
        }
        .version-info {
          color: #e8741a;
        }
      }

      &.fr {
        .version-box-item .version-label {
          width: 180px;
        }
      }
    }
  }
</style>
