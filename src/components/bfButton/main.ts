import { defineComponent, h, computed } from 'vue'
import { ElButton } from 'element-plus'
import './main.scss'

interface BfBtnProps {
  borderWidth?: number
  width?: string
  height?: string
  fontSize?: string
  backgroundColor?: string
  color?: string
  boxShadow?: string
}

export default defineComponent({
  name: 'BfBtn',
  props: {
    borderWidth: { type: Number, default: 1 },
    width: { type: String, default: 'auto' },
    height: { type: String, default: 'auto' },
    fontSize: { type: String, default: '32px' },
    backgroundColor: { type: String, default: '#7575752B' },
    color: { type: String, default: '#FF4A4A' },
    boxShadow: { type: String, default: '0 4px 8px rgba(0, 0, 0, 0.2)' },
    // 将 ElButton 的属性作为组件的 props
    ...ElButton.props,
  },
  setup(props: BfBtnProps & InstanceType<typeof ElButton>['$props'], { slots, attrs }) {
    const componentStyle = computed(() => {
      const clipPathValue = `polygon(
        8px 0,                       /* 左上角切角 */
        100% 0,
        100% calc(100% - 11px),      /* 右下角切角 */
        calc(100% - 11px) 100%,      /* 右下角切角 */
        0 100%,
        0 8px                        /* 左上角切角 */
      )`

      return {
        '--button-width': props.width,
        '--button-height': props.height,
        '--font-size': props.fontSize,
        '--background-color': props.backgroundColor,
        '--color': props.color,
        '--clip-path-value': clipPathValue,
        '--border-width': `${props.borderWidth}px`,
        '--box-shadow': props.boxShadow,
      }
    })

    // 构建传递给 ElButton 的属性对象
    const buttonProps = computed(() => {
      const { borderWidth: _borderWidth, width: _width, height: _height, fontSize: _fontSize, backgroundColor: _backgroundColor, color: _color, boxShadow: _boxShadow, ...restProps } = props
      return {
        ...attrs,
        ...restProps,
      }
    })

    // 创建角落三角形元素
    const createCornerTriangle = (position: 'top-left' | 'bottom-right') => {
      return h('i', {
        class: ['corner-triangle', position],
      })
    }

    // 使用 h 函数渲染组件
    return () =>
      h(
        'div',
        {
          class: 'clipped-el-button-wrapper',
          style: componentStyle.value,
        },
        [
          createCornerTriangle('top-left'),
          createCornerTriangle('bottom-right'),
          h(
            ElButton,
            {
              ...buttonProps.value,
            },
            slots
          ),
        ]
      )
  },
})