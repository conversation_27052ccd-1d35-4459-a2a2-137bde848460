import { defineComponent, h, computed, ref } from 'vue'
import { ElDialog } from 'element-plus'
import './main.scss'
import closeImg from '@/assets/images/common/close.png'

export default defineComponent({
  name: 'BfDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    // 将 ElDialog 的属性作为组件的 props
    ...ElDialog.props,
  },
  emits: ['update:modelValue'],
  setup(props: InstanceType<typeof ElDialog>['$props'], { emit, slots, expose, attrs }) {
    const visible = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })
    const CloseIcon = h('span', {
      class: 'w-full h-full',
      style: {
        backgroundImage: `url(${closeImg})`,
        backgroundColor: 'transparent',
        backgroundSize: '100% 100%',
      },
    })

    // 构建传递给 ElDialog 的属性对象
    const dialogProps = computed(() => {
      return {
        ...attrs,
        ...props,
        closeIcon: props.closeIcon ?? CloseIcon,
        destroyOnClose: props.destroyOnClose ?? true,
        class: ['bf-dialog', attrs.class].filter(Boolean).join(' '),
        modelValue: visible.value,
        'onUpdate:modelValue': (val: boolean) => {
          visible.value = val
        },
      }
    })

    // 向父组件暴露 dialogRef
    const dialogRef = ref<InstanceType<typeof ElDialog>>()
    expose({
      dialogRef,
    })

    // 使用 h 函数渲染 ElDialog
    return () =>
      h(
        ElDialog,
        {
          ...dialogProps.value,
          ref: dialogRef,
        },
        slots
      )
  },
})
