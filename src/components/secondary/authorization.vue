<template>
  <template v-if="isRequestAuth">
    <el-form ref="requestLicenseForm" :model="licData" :rules="licDataRules" label-position="right" label-width="100px" class="mt-2 request-license-form">
      <el-form-item prop="projName" class="proj-name">
        <template #label>
          <span class="form-item-label" :title="$t('auth.projName')">{{ $t('auth.projName') }}</span>
        </template>
        <el-input v-model="licData.projName" :maxlength="16" />
      </el-form-item>
      <el-form-item :label="$t('auth.authorizedModules')">
        <div class="el-checkbox-group">
          <template v-for="(val, key) in moduleList" :key="key">
            <el-checkbox v-model="licData.lic8100Content[key]" :label="$t(getAuthModuleI18nKey(val))" :true-value="1" :false-value="4" />
          </template>
        </div>
      </el-form-item>
      <el-form-item class="authorized-number" :label="$t('auth.authorizedNumber')">
        <div v-for="(val, key) in maxModuleList" :key="key" class="flex gap-1 justify-between my-1.5 mr-2 authorize-number-input">
          <span>{{ $t(getAuthModuleI18nKey(val)) }}</span>
          <el-input-number v-model="licData.lic8100Content[key]" :min="0" :max="**********" :controls="false" :placeholder="$t('auth.unlimited')" />
        </div>
      </el-form-item>
      <el-form-item prop="note" :label="$t('auth.authDescription')">
        <el-input v-model="licData.note" :maxlength="256" type="textarea" :rows="3" :autosize="{ minRows: 2, maxRows: 5 }" resize="none" />
      </el-form-item>
      <div class="my-2 text-center auth-actions request-license-actions">
        <BfBtn width="120px" height="40px" fontSize="16px" @click="backToAuthInfo">
          {{ $t('dialog.back') }}
        </BfBtn>
        <BfBtn width="120px" height="40px" fontSize="16px" type="primary" @click="generateLicense">
          {{ $t('dialog.confirm') }}
        </BfBtn>
      </div>
    </el-form>
  </template>
  <!-- 显示已授权的信息 -->
  <template v-else-if="licenseIsExist">
    <div class="flex flex-col gap-2 mt-2 auth-container">
      <div class="auth-item">
        <div class="label">{{ $t('auth.projName') }}:</div>
        <div class="info">
          {{ license.projName }}
        </div>
      </div>

      <template v-if="license.lic">
        <div class="auth-item">
          <div class="label">{{ $t('auth.expireTime') }}:</div>
          <div class="info">
            {{ expireTimeLabel }}
            <!-- 到期时间剩余1年,在时间后面添加上剩余的时间 -->
            <div v-if="remainingExpirationTime.days <= 365" :class="['remaining-expiration-time', { warning: remainingExpirationTime.days <= 30 }]">
              {{ $t('auth.remainingExpirationTime', remainingExpirationTime) }}
            </div>
          </div>
        </div>

        <div v-if="licenses" class="auth-item">
          <div class="label">{{ $t('auth.authorizedContent') }}:</div>
          <div class="flex flex-col flex-wrap text-left info license-content">
            <el-checkbox v-for="(val, key) in licenses" :key="key" :label="getAuthorizedModuleLabel(key, val.limit)" :model-value="val.value" class="!h-7" />
          </div>
        </div>
      </template>
    </div>
    <div class="my-2 text-center auth-actions">
      <BfBtn width="122px" height="43px" fontSize="16px" boxShadow="inset 0px -8px 6px rgba(253, 162, 22, 0.16)" color="#FDA216" backgroundColor="#FDA2162B" @click="jumpToRequestLicense(true)">
        {{ $t('auth.applyAuth') }}
      </BfBtn>
      <BfBtn width="140px" height="40px" fontSize="16px" type="primary" plain @click="$refs.licenseFileInput.click()">
        {{ $t('auth.importAuthFile') }}
        <input ref="licenseFileInput" type="file" hidden accept=".lic" @change="evt => importAndUploadLicense(evt.target.files)" />
      </BfBtn>
    </div>
  </template>
  <!-- 显示没有授权的提示 -->
  <template v-else>
    <div class="no-auth">
      {{ $t('auth.noAuthAlert') }}
    </div>
    <div class="my-2 text-center auth-actions">
      <BfBtn width="122px" height="43px" fontSize="16px" boxShadow="inset 0px -8px 6px rgba(253, 162, 22, 0.16)" color="#FDA216" backgroundColor="#FDA2162B" @click="jumpToRequestLicense(false)">
        {{ $t('auth.applyAuth') }}
      </BfBtn>
    </div>
  </template>
</template>

<script>
  import vueMixin from '@/utils/vueMixin'
  import {
    calcRemainingExpirationTime,
    getAuthModuleI18nKey,
    getLicense,
    isAuthData,
    isAuthForEver,
    isAuthModule,
    licenseIsExist,
    LicenseApplicationModuleNames,
    LicenseModuleNames,
    requestCurrentLicense,
    requestGenerateLicense,
    saveLicense,
    uploadLicenseFile,
  } from '@/utils/bfAuth'
  import { messageBox } from '@/utils/notify'
  import { utcToLocalTime, DateMask } from '@/utils/time'
  import validateRules from '@/utils/validateRules'
  import bfutil from '@/utils/bfutil'
  import dayjs from 'dayjs'
  import bfproto, { bfdxProtoPackageName } from '@/modules/protocol'
  import base64js from 'base64-js'
  import BfBtn from '@/components/bfButton'

  export default {
    name: 'BfAuthorization',
    components: {
      BfBtn,
    },
    mixins: [vueMixin],
    data() {
      return {
        visible: false,
        license: {},
        // 标记是否正在申请授权
        isRequestAuth: false,
        licData: {
          projName: '',
          note: '',
          lic8100Content: {
            modRfid: 4,
            modRecord: 4,
            modPhoneGateway: 4,
            modDispatch: 4,
            modTraditionalDmr: 4,
            modSvt: 4,
            maxControllers: undefined,
            maxDevices: undefined,
            maxUsers: undefined,
          },
        },
      }
    },
    computed: {
      moduleList() {
        return Object.keys(LicenseApplicationModuleNames)
          .filter(key => isAuthModule(LicenseApplicationModuleNames[key]))
          .map(key => ({ [key]: LicenseApplicationModuleNames[key] }))
          .reduce((p, c) => Object.assign(p, c), {})
      },
      maxModuleList() {
        return Object.keys(LicenseApplicationModuleNames)
          .filter(key => !isAuthModule(LicenseApplicationModuleNames[key]))
          .map(key => ({ [key]: LicenseApplicationModuleNames[key] }))
          .reduce((p, c) => Object.assign(p, c), {})
      },
      licenses() {
        const licenses = this.license.lic?.licenses
        if (!licenses) {
          return undefined
        }

        return Object.values(LicenseModuleNames)
          .map(key => {
            const isData = isAuthData(key)
            return {
              [key]: {
                value: isData || licenses[key] === 0 || licenses[key] === 1,
                limit: isData ? (licenses[key] ?? 0) : -1,
              },
            }
          })
          .reduce((p, c) => Object.assign(p, c), {})
      },
      expireTimeLabel() {
        const expireTime = this.license.lic.expireTime
        // 判断是否永久授权
        if (isAuthForEver(expireTime)) {
          return this.$t('auth.permanent')
        }

        return utcToLocalTime(expireTime, DateMask)
      },
      remainingExpirationTime() {
        return calcRemainingExpirationTime(this.license.lic.expireTime)
      },
      licenseIsExist() {
        return licenseIsExist(this.license)
      },
      licDataRules() {
        return {
          projName: [validateRules.required()],
          note: [],
        }
      },
    },
    methods: {
      closeDlgFn() {
        // 使用定时器在对话窗口关闭后重置申请授权状态，避免v-if控制的元素闪烁
        setTimeout(() => {
          this.isRequestAuth = false
        }, 350)
      },
      isAuthModule: isAuthModule,
      isAuthData: isAuthData,
      getAuthModuleI18nKey: getAuthModuleI18nKey,
      /**
       * @param lic {lic_response}
       */
      syncLicense(lic) {
        this.license = { ...lic }
      },
      /**
       * 请求当前的授权信息
       * @param {boolean} silent 是否静默请求，不弹出任何提示
       * @return {Promise<void>}
       */
      async requestLicense(silent = false) {
        const lic = await requestCurrentLicense()
        if (licenseIsExist(lic)) {
          this.syncLicense(lic)
          saveLicense(lic)
        } else {
          !silent && messageBox(this.$t('auth.queryAuthFailed'))
        }
      },
      /**
       * 请求服务器生成授权数据
       * @returns {Promise<void>}
       */
      async generateLicense() {
        // 先表单检验
        const valid = await this.$refs.requestLicenseForm.validate().catch(() => false)
        if (!valid) {
          return
        }

        const lic8100Content = {
          ...this.licData.lic8100Content,
          maxControllers: this.licData.lic8100Content.maxControllers ?? 0,
          maxDevices: this.licData.lic8100Content.maxDevices ?? 0,
          maxUsers: this.licData.lic8100Content.maxUsers ?? 0,
        }
        const contentType = bfproto.bfdx_proto_msg_T('lic_8100_content', bfdxProtoPackageName)
        const content = contentType.encode(contentType.create(lic8100Content)).finish()

        const licRequestType = bfproto.bfdx_proto_msg_T('lic_request', bfdxProtoPackageName)
        const licRequestData = base64js.fromByteArray(licRequestType.encode(licRequestType.create({ userLicContent: content })).finish())
        // licData 填写data(base64(lic_request))、proj_name和note
        const licRequestFile = await requestGenerateLicense({
          projName: this.licData.projName,
          note: this.licData.note,
          data: licRequestData,
        })
        if (licRequestFile) {
          messageBox(this.$t('auth.requestAuthSuccess'))
          this.requestLicense(true)
          // 下载授权申请文件
          const timeSuffix = dayjs().format('YYYYMMDDHHmmss')
          bfutil.saveAsFile(`${this.licData.projName}-${timeSuffix}.req`, licRequestFile.buffer)
        }
      },

      /**
       * 导入并上传授权文件
       * @param {*} files DOM选中的文件对象
       */
      importAndUploadLicense(files) {
        console.log('importAndUploadLicense:', files)
        const reader = new FileReader()
        reader.onload = async () => {
          // 上传到服务器,成功后,查询并覆盖当前授权许可
          const ok = await uploadLicenseFile(new Uint8Array(reader.result))
          if (ok) {
            messageBox(this.$t('msgbox.importSuccess'))
            this.requestLicense(true)
            this.backToAuthInfo()
          }
        }
        reader.readAsArrayBuffer(files[0])

        // 清空选择文件输入框内容,解决不能重复选择同一个文件问题
        this.$nextTick(() => {
          this.$refs.licenseFileInput.value = ''
        })
      },
      /**
       * 跳转到申请授权的页面
       * @param {boolean} isRenewal 是否续期
       */
      jumpToRequestLicense(isRenewal = false) {
        this.isRequestAuth = true
        // 续期,使用现有的projName
        if (isRenewal) {
          this.licData.projName = this.license.projName
        }
      },
      backToAuthInfo() {
        this.isRequestAuth = false
        // 清除设置的申请授权的参数
        this.licData.projName = ''
        this.licData.note = ''
      },
      utcToLocalTime: utcToLocalTime,
      /**
       * 获取被授权的模块的标签内容
       * @param {keyof LicenseModuleNames} moduleName
       * @param {number} limit 当授权模块为数据授权时，此值为数量限制
       */
      getAuthorizedModuleLabel(moduleName, limit) {
        if (isAuthModule(moduleName)) {
          return this.$t(getAuthModuleI18nKey(moduleName))
        } else if (isAuthData(moduleName)) {
          return this.$t(getAuthModuleI18nKey(moduleName)) + ': ' + (limit > 0 ? limit : this.$t('auth.unlimited'))
        } else {
          return undefined
        }
      },
    },
    beforeMount() {
      const lic = getLicense()
      if (licenseIsExist(lic)) {
        this.syncLicense(lic)
      }

      this.requestLicense(true)
    },
  }
</script>

<style lang="scss">
  @use '@/css/common.scss' as *;

  .el-dialog.auth-info {
    width: 560px;
    height: 416px;

    .el-dialog__body {
      display: flex;
      flex-direction: column;
      padding: 0 10px;
      justify-content: space-around;

      .auth-actions {
        .el-button {
          min-width: 30%;
        }
      }

      .request-license-form {
        .el-form-item {
          margin-bottom: 12px;
        }
      }

      .auth-container .auth-item {
        display: flex;

        .label {
          flex-basis: 35%;
          flex-shrink: 0;
          margin-right: 10px;
          text-align: right;
        }

        .info.license-content {
          .el-checkbox__label {
            white-space: normal;
            word-break: break-word;
          }
        }

        .remaining-expiration-time {
          display: inline-block;
        }

        .remaining-expiration-time.warning {
          //color: #C10015
          color: red;
        }
      }

      .el-form-item {
        .el-checkbox-group {
          outline: 0;
          padding: 5px 15px 0;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          line-height: 24px;
        }

        .form-item-label {
          display: block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    .authorized-number {
      > .el-form-item__content {
        outline: 0;
        padding: 0 15px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
      }
    }
  }
</style>
